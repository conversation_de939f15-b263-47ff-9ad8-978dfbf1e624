# ShinKenShinKunServer IIS Deployment Script
# This script builds and deploys the ShinKenShinKunServer to IIS
# Target Path: C:\inetpub\wwwroot\shin.local

param(
    [string]$TargetPath = "C:\inetpub\wwwroot\shin.local",
    [string]$SiteName = "shin.local",
    [string]$AppPoolName = "ShinKenShinKunServerPool",
    [string]$BuildConfiguration = "Release",
    [switch]$CreateSite = $false,
    [switch]$Force = $false
)

# Import required modules
Import-Module WebAdministration -ErrorAction SilentlyContinue

# Script variables
$ScriptPath = Split-Path -Parent $MyInvocation.MyCommand.Definition
$ProjectPath = Join-Path $ScriptPath "src\Presentation\WebApi\ShinKenShinKunServer"
$ProjectFile = Join-Path $ProjectPath "ShinKenShinKunServer.csproj"
$SolutionFile = Join-Path $ScriptPath "ShinKenShinKun.sln"

Write-Host "=== ShinKenShinKunServer IIS Deployment Script ===" -ForegroundColor Green
Write-Host "Target Path: $TargetPath" -ForegroundColor Yellow
Write-Host "Site Name: $SiteName" -ForegroundColor Yellow
Write-Host "App Pool: $AppPoolName" -ForegroundColor Yellow
Write-Host "Build Configuration: $BuildConfiguration" -ForegroundColor Yellow

# Function to check if running as administrator
function Test-Administrator {
    $currentUser = [Security.Principal.WindowsIdentity]::GetCurrent()
    $principal = New-Object Security.Principal.WindowsPrincipal($currentUser)
    return $principal.IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)
}

# Function to find MSBuild
function Find-MSBuild {
    $msbuildPaths = @(
        "${env:ProgramFiles}\Microsoft Visual Studio\2022\Enterprise\MSBuild\Current\Bin\MSBuild.exe",
        "${env:ProgramFiles}\Microsoft Visual Studio\2022\Professional\MSBuild\Current\Bin\MSBuild.exe",
        "${env:ProgramFiles}\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MSBuild.exe",
        "${env:ProgramFiles(x86)}\Microsoft Visual Studio\2019\Enterprise\MSBuild\Current\Bin\MSBuild.exe",
        "${env:ProgramFiles(x86)}\Microsoft Visual Studio\2019\Professional\MSBuild\Current\Bin\MSBuild.exe",
        "${env:ProgramFiles(x86)}\Microsoft Visual Studio\2019\Community\MSBuild\Current\Bin\MSBuild.exe",
        "${env:ProgramFiles(x86)}\Microsoft Visual Studio\2017\Enterprise\MSBuild\15.0\Bin\MSBuild.exe",
        "${env:ProgramFiles(x86)}\Microsoft Visual Studio\2017\Professional\MSBuild\15.0\Bin\MSBuild.exe",
        "${env:ProgramFiles(x86)}\Microsoft Visual Studio\2017\Community\MSBuild\15.0\Bin\MSBuild.exe",
        "${env:ProgramFiles(x86)}\MSBuild\14.0\Bin\MSBuild.exe"
    )
    
    foreach ($path in $msbuildPaths) {
        if (Test-Path $path) {
            return $path
        }
    }
    
    # Try to find MSBuild in PATH
    $msbuild = Get-Command "msbuild.exe" -ErrorAction SilentlyContinue
    if ($msbuild) {
        return $msbuild.Source
    }
    
    throw "MSBuild not found. Please install Visual Studio or Build Tools for Visual Studio."
}

# Function to create application pool
function New-ApplicationPool {
    param([string]$Name)
    
    Write-Host "Creating application pool: $Name" -ForegroundColor Cyan
    
    if (Get-IISAppPool -Name $Name -ErrorAction SilentlyContinue) {
        if ($Force) {
            Write-Host "Removing existing application pool: $Name" -ForegroundColor Yellow
            Remove-WebAppPool -Name $Name
        } else {
            Write-Host "Application pool already exists: $Name" -ForegroundColor Yellow
            return
        }
    }
    
    New-WebAppPool -Name $Name
    Set-ItemProperty -Path "IIS:\AppPools\$Name" -Name "processModel.identityType" -Value "ApplicationPoolIdentity"
    Set-ItemProperty -Path "IIS:\AppPools\$Name" -Name "managedRuntimeVersion" -Value "v4.0"
    Set-ItemProperty -Path "IIS:\AppPools\$Name" -Name "enable32BitAppOnWin64" -Value $false
    
    Write-Host "Application pool created successfully: $Name" -ForegroundColor Green
}

# Function to create IIS site
function New-IISSite {
    param(
        [string]$Name,
        [string]$Path,
        [string]$AppPool,
        [int]$Port = 80
    )
    
    Write-Host "Creating IIS site: $Name" -ForegroundColor Cyan
    
    if (Get-Website -Name $Name -ErrorAction SilentlyContinue) {
        if ($Force) {
            Write-Host "Removing existing site: $Name" -ForegroundColor Yellow
            Remove-Website -Name $Name
        } else {
            Write-Host "Site already exists: $Name" -ForegroundColor Yellow
            return
        }
    }
    
    New-Website -Name $Name -PhysicalPath $Path -ApplicationPool $AppPool -Port $Port
    Write-Host "IIS site created successfully: $Name" -ForegroundColor Green
}

# Function to set folder permissions
function Set-FolderPermissions {
    param([string]$Path)
    
    Write-Host "Setting folder permissions for: $Path" -ForegroundColor Cyan
    
    # Grant IIS_IUSRS read and execute permissions
    $acl = Get-Acl $Path
    $accessRule = New-Object System.Security.AccessControl.FileSystemAccessRule("IIS_IUSRS", "ReadAndExecute", "ContainerInherit,ObjectInherit", "None", "Allow")
    $acl.SetAccessRule($accessRule)
    
    # Grant application pool identity modify permissions to App_Data and Logs if they exist
    $appDataPath = Join-Path $Path "App_Data"
    $logsPath = Join-Path $Path "Logs"
    
    if (Test-Path $appDataPath) {
        $accessRule = New-Object System.Security.AccessControl.FileSystemAccessRule("IIS AppPool\$AppPoolName", "Modify", "ContainerInherit,ObjectInherit", "None", "Allow")
        $acl.SetAccessRule($accessRule)
    }
    
    Set-Acl -Path $Path -AclObject $acl
    Write-Host "Folder permissions set successfully" -ForegroundColor Green
}

# Main deployment logic starts here
try {
    # Check if running as administrator
    if (-not (Test-Administrator)) {
        throw "This script must be run as Administrator to deploy to IIS."
    }
    
    # Verify project files exist
    if (-not (Test-Path $ProjectFile)) {
        throw "Project file not found: $ProjectFile"
    }
    
    if (-not (Test-Path $SolutionFile)) {
        throw "Solution file not found: $SolutionFile"
    }
    
    # Find MSBuild
    $msbuildPath = Find-MSBuild
    Write-Host "Using MSBuild: $msbuildPath" -ForegroundColor Cyan
    
    # Build the project
    Write-Host "Building ShinKenShinKunServer..." -ForegroundColor Cyan
    $buildArgs = @(
        $ProjectFile,
        "/p:Configuration=$BuildConfiguration",
        "/p:Platform=`"Any CPU`"",
        "/p:OutputPath=bin\",
        "/t:Build",
        "/verbosity:minimal"
    )
    
    & $msbuildPath $buildArgs
    
    if ($LASTEXITCODE -ne 0) {
        throw "Build failed with exit code: $LASTEXITCODE"
    }
    
    Write-Host "Build completed successfully" -ForegroundColor Green

    # Create target directory if it doesn't exist
    if (-not (Test-Path $TargetPath)) {
        Write-Host "Creating target directory: $TargetPath" -ForegroundColor Cyan
        New-Item -ItemType Directory -Path $TargetPath -Force | Out-Null
    } elseif ($Force) {
        Write-Host "Cleaning target directory: $TargetPath" -ForegroundColor Yellow
        Get-ChildItem -Path $TargetPath -Recurse | Remove-Item -Force -Recurse
    }

    # Copy application files
    Write-Host "Copying application files to: $TargetPath" -ForegroundColor Cyan

    # Define files and folders to copy
    $itemsToCopy = @(
        "*.aspx",
        "*.asmx",
        "*.html",
        "*.htm",
        "*.css",
        "*.js",
        "*.config",
        "bin",
        "App_Data",
        "Images",
        "Scripts",
        "Content"
    )

    foreach ($item in $itemsToCopy) {
        $sourcePath = Join-Path $ProjectPath $item
        if (Test-Path $sourcePath) {
            Write-Host "  Copying: $item" -ForegroundColor Gray
            if (Test-Path $sourcePath -PathType Container) {
                # Copy directory
                $destPath = Join-Path $TargetPath (Split-Path $sourcePath -Leaf)
                Copy-Item -Path $sourcePath -Destination $destPath -Recurse -Force
            } else {
                # Copy files matching pattern
                Copy-Item -Path $sourcePath -Destination $TargetPath -Force
            }
        }
    }

    # Copy specific files that are required
    $requiredFiles = @(
        "Web.config",
        "index.html",
        "PublicService.asmx"
    )

    foreach ($file in $requiredFiles) {
        $sourcePath = Join-Path $ProjectPath $file
        if (Test-Path $sourcePath) {
            Write-Host "  Copying required file: $file" -ForegroundColor Gray
            Copy-Item -Path $sourcePath -Destination $TargetPath -Force
        }
    }

    # Ensure bin directory and its contents are copied
    $binSource = Join-Path $ProjectPath "bin"
    $binDest = Join-Path $TargetPath "bin"
    if (Test-Path $binSource) {
        Write-Host "  Copying bin directory..." -ForegroundColor Gray
        if (Test-Path $binDest) {
            Remove-Item -Path $binDest -Recurse -Force
        }
        Copy-Item -Path $binSource -Destination $binDest -Recurse -Force
    }

    Write-Host "Application files copied successfully" -ForegroundColor Green

    # Create application pool if it doesn't exist
    New-ApplicationPool -Name $AppPoolName

    # Create IIS site if requested
    if ($CreateSite) {
        New-IISSite -Name $SiteName -Path $TargetPath -AppPool $AppPoolName
    } else {
        # Check if we need to create an application under Default Web Site
        $defaultSite = "Default Web Site"
        if (Get-Website -Name $defaultSite -ErrorAction SilentlyContinue) {
            $appPath = "/$SiteName"

            # Remove existing application if Force is specified
            if ($Force -and (Get-WebApplication -Site $defaultSite -Name $SiteName -ErrorAction SilentlyContinue)) {
                Write-Host "Removing existing application: $appPath" -ForegroundColor Yellow
                Remove-WebApplication -Site $defaultSite -Name $SiteName
            }

            # Create application under Default Web Site
            if (-not (Get-WebApplication -Site $defaultSite -Name $SiteName -ErrorAction SilentlyContinue)) {
                Write-Host "Creating application under Default Web Site: $appPath" -ForegroundColor Cyan
                New-WebApplication -Site $defaultSite -Name $SiteName -PhysicalPath $TargetPath -ApplicationPool $AppPoolName
                Write-Host "Application created successfully: $appPath" -ForegroundColor Green
            } else {
                Write-Host "Application already exists: $appPath" -ForegroundColor Yellow
            }
        }
    }

    # Set folder permissions
    Set-FolderPermissions -Path $TargetPath

    # Create logs directory if specified in config
    $logsPath = "C:\Arctec\Log"
    if (-not (Test-Path $logsPath)) {
        Write-Host "Creating logs directory: $logsPath" -ForegroundColor Cyan
        New-Item -ItemType Directory -Path $logsPath -Force | Out-Null

        # Set permissions for logs directory
        $acl = Get-Acl $logsPath
        $accessRule = New-Object System.Security.AccessControl.FileSystemAccessRule("IIS AppPool\$AppPoolName", "FullControl", "ContainerInherit,ObjectInherit", "None", "Allow")
        $acl.SetAccessRule($accessRule)
        Set-Acl -Path $logsPath -AclObject $acl

        Write-Host "Logs directory created and configured: $logsPath" -ForegroundColor Green
    }

    # Display deployment summary
    Write-Host ""
    Write-Host "=== Deployment Summary ===" -ForegroundColor Green
    Write-Host "Project: ShinKenShinKunServer" -ForegroundColor White
    Write-Host "Target Path: $TargetPath" -ForegroundColor White
    Write-Host "Application Pool: $AppPoolName" -ForegroundColor White
    if ($CreateSite) {
        Write-Host "IIS Site: $SiteName" -ForegroundColor White
        Write-Host "URL: http://localhost/$SiteName" -ForegroundColor White
    } else {
        Write-Host "Application: /$SiteName (under Default Web Site)" -ForegroundColor White
        Write-Host "URL: http://localhost/$SiteName" -ForegroundColor White
    }
    Write-Host "Service URL: http://localhost/$SiteName/PublicService.asmx" -ForegroundColor White
    Write-Host ""
    Write-Host "Deployment completed successfully!" -ForegroundColor Green

} catch {
    Write-Host ""
    Write-Host "=== Deployment Failed ===" -ForegroundColor Red
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host ""
    exit 1
}
