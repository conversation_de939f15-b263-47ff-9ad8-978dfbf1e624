# Manual IIS Setup Guide for ShinKenShinKunServer

If the automated scripts are not working, follow these manual steps to configure IIS.

## Prerequisites Check

### 1. Verify IIS Installation
Open PowerShell as Administrator and run:
```powershell
Get-WindowsFeature -Name *IIS*
```

### 2. Enable Required IIS Features (if not installed)
```powershell
Enable-WindowsOptionalFeature -Online -FeatureName IIS-WebServerRole,IIS-WebServer,IIS-CommonHttpFeatures,IIS-HttpErrors,IIS-HttpLogging,IIS-RequestFiltering,IIS-StaticContent,IIS-DefaultDocument,IIS-DirectoryBrowsing,IIS-ASPNET45,IIS-NetFxExtensibility45,IIS-ISAPIExtensions,IIS-ISAPIFilter,IIS-ManagementConsole -All
```

## Manual IIS Configuration Steps

### Step 1: Open IIS Manager
1. Press `Win + R`, type `inetmgr`, press Enter
2. Or go to Start → Administrative Tools → Internet Information Services (IIS) Manager

### Step 2: Create Application Pool

1. **Expand the server node** in the left panel
2. **Right-click "Application Pools"** → **"Add Application Pool..."**
3. **Configure the Application Pool:**
   - **Name:** `ShinKenShinKunServerPool`
   - **.NET CLR Version:** `.NET CLR Version v4.0.30319`
   - **Managed Pipeline Mode:** `Integrated`
   - Click **"OK"**

4. **Configure Advanced Settings:**
   - **Right-click** the new application pool → **"Advanced Settings..."**
   - **Process Model → Identity:** `ApplicationPoolIdentity`
   - **Process Model → Idle Time-out (minutes):** `0` (to prevent timeout)
   - **Recycling → Regular Time Interval (minutes):** `0` (to prevent automatic recycling)
   - Click **"OK"**

5. **Start the Application Pool:**
   - **Right-click** the application pool → **"Start"**

### Step 3: Create Web Application

1. **Expand "Sites"** in the left panel
2. **Expand "Default Web Site"**
3. **Right-click "Default Web Site"** → **"Add Application..."**
4. **Configure the Application:**
   - **Alias:** `shin.local`
   - **Application Pool:** `ShinKenShinKunServerPool` (select from dropdown)
   - **Physical Path:** `C:\inetpub\wwwroot\shin.local`
   - Click **"OK"**

### Step 4: Verify Application Settings

1. **Click on the "shin.local" application** in the left panel
2. **Double-click "Handler Mappings"** in the main panel
3. **Verify these handlers exist:**
   - `*.asmx` → `System.Web.Services.Protocols.WebServiceHandlerFactory`
   - `*.aspx` → `System.Web.UI.PageHandlerFactory`

If missing, add them:
- **Right-click** → **"Add Managed Handler..."**
- **Request path:** `*.asmx`
- **Type:** `System.Web.Services.Protocols.WebServiceHandlerFactory`
- **Name:** `WebServiceHandlerFactory-ISAPI-4.0_64bit`

### Step 5: Set Folder Permissions

#### Using File Explorer:
1. **Navigate to:** `C:\inetpub\wwwroot\shin.local`
2. **Right-click** the folder → **"Properties"** → **"Security"** tab
3. **Click "Edit..."** → **"Add..."**
4. **Add these users/groups:**

   **IIS_IUSRS:**
   - Type: `IIS_IUSRS`
   - Permissions: `Read & Execute`, `List folder contents`, `Read`

   **Application Pool Identity:**
   - Type: `IIS AppPool\ShinKenShinKunServerPool`
   - Permissions: `Modify`, `Read & Execute`, `List folder contents`, `Read`, `Write`

#### Using Command Line (Alternative):
```cmd
REM Grant IIS_IUSRS read access
icacls "C:\inetpub\wwwroot\shin.local" /grant "IIS_IUSRS:(OI)(CI)RX"

REM Grant application pool identity modify access
icacls "C:\inetpub\wwwroot\shin.local" /grant "IIS AppPool\ShinKenShinKunServerPool:(OI)(CI)M"
```

### Step 6: Configure Logs Directory

1. **Create logs directory:**
   ```cmd
   mkdir "C:\Arctec\Log"
   ```

2. **Set permissions:**
   ```cmd
   icacls "C:\Arctec\Log" /grant "IIS AppPool\ShinKenShinKunServerPool:(OI)(CI)F"
   ```

### Step 7: Restart IIS

Open Command Prompt as Administrator and run:
```cmd
iisreset
```

## Verification Steps

### 1. Check Application Pool Status
1. In IIS Manager, click **"Application Pools"**
2. Verify `ShinKenShinKunServerPool` shows **"Started"**

### 2. Check Web Application
1. In IIS Manager, expand **"Sites"** → **"Default Web Site"**
2. Verify `shin.local` application exists

### 3. Test URLs
Open a web browser and test:
- **Home Page:** `http://localhost/shin.local`
- **Web Service:** `http://localhost/shin.local/PublicService.asmx`
- **WSDL:** `http://localhost/shin.local/PublicService.asmx?WSDL`

## Troubleshooting Common Issues

### Issue: "HTTP Error 500.19 - Internal Server Error"
**Solution:** Check that ASP.NET 4.8 is properly installed and registered with IIS:
```cmd
%windir%\Microsoft.NET\Framework64\v4.0.30319\aspnet_regiis.exe -ir
```

### Issue: "HTTP Error 404.17 - Not Found"
**Solution:** Ensure ASMX handler is registered:
1. Go to IIS Manager → Application → Handler Mappings
2. Add `*.asmx` handler if missing

### Issue: Application Pool Stops Frequently
**Solution:** 
1. Check Event Viewer for errors
2. Verify folder permissions
3. Check Web.config for configuration errors

### Issue: "Access Denied" Errors
**Solution:** 
1. Verify IIS_IUSRS has read permissions
2. Verify Application Pool identity has modify permissions
3. Check that the application pool identity can access the database

### Issue: Database Connection Errors
**Solution:** 
1. Update connection strings in Web.config
2. Ensure SQL Server is running
3. Verify database user permissions

## Configuration Files to Check

### Web.config Location
`C:\inetpub\wwwroot\shin.local\Web.config`

### Key Settings to Verify
```xml
<system.web>
    <compilation debug="false" targetFramework="4.8" />
    <authentication mode="Windows" />
</system.web>
```

### Connection Strings
Update these in Web.config as needed:
```xml
<connectionStrings>
    <add name="MedicalCheckupConnectionString" 
         connectionString="Data Source=localhost,1435;Initial Catalog=GenkiPlazaDatabase2;Persist Security Info=True;User ID=sa;Password=YourPassword;TrustServerCertificate=True" 
         providerName="System.Data.SqlClient"/>
</connectionStrings>
```

## Final Verification

Run the verification script to confirm everything is working:
```powershell
.\verify-deployment.ps1
```

If all steps are completed correctly, you should see:
- ✅ All required files exist
- ✅ Application pool is running
- ✅ Web application is configured
- ✅ URLs are accessible
- ✅ Permissions are set correctly
