# ShinKenShinKunServer Deployment Verification Script
# This script verifies that the deployment was successful

param(
    [string]$TargetPath = "C:\inetpub\wwwroot\shin.local",
    [string]$SiteName = "shin.local",
    [string]$AppPoolName = "ShinKenShinKunServerPool",
    [string]$BaseUrl = "http://localhost"
)

# Import required modules
Import-Module WebAdministration -ErrorAction SilentlyContinue

Write-Host "=== ShinKenShinKunServer Deployment Verification ===" -ForegroundColor Green
Write-Host "Target Path: $TargetPath" -ForegroundColor Yellow
Write-Host "Site Name: $SiteName" -ForegroundColor Yellow
Write-Host "App Pool: $AppPoolName" -ForegroundColor Yellow
Write-Host "Base URL: $BaseUrl" -ForegroundColor Yellow
Write-Host ""

$errors = @()
$warnings = @()

# Function to add error
function Add-Error {
    param([string]$Message)
    $script:errors += $Message
    Write-Host "❌ ERROR: $Message" -ForegroundColor Red
}

# Function to add warning
function Add-Warning {
    param([string]$Message)
    $script:warnings += $Message
    Write-Host "⚠️  WARNING: $Message" -ForegroundColor Yellow
}

# Function to add success
function Add-Success {
    param([string]$Message)
    Write-Host "✅ $Message" -ForegroundColor Green
}

# Check if target directory exists
Write-Host "Checking deployment files..." -ForegroundColor Cyan
if (Test-Path $TargetPath) {
    Add-Success "Target directory exists: $TargetPath"
} else {
    Add-Error "Target directory not found: $TargetPath"
}

# Check required files
$requiredFiles = @(
    "Web.config",
    "index.html", 
    "PublicService.asmx",
    "bin\ShinKenShinKunServer.dll"
)

foreach ($file in $requiredFiles) {
    $filePath = Join-Path $TargetPath $file
    if (Test-Path $filePath) {
        Add-Success "Required file exists: $file"
    } else {
        Add-Error "Required file missing: $file"
    }
}

# Check IIS configuration
Write-Host ""
Write-Host "Checking IIS configuration..." -ForegroundColor Cyan

# Check if WebAdministration module is available
if (Get-Module -Name WebAdministration -ListAvailable) {
    # Check application pool
    $appPool = Get-IISAppPool -Name $AppPoolName -ErrorAction SilentlyContinue
    if ($appPool) {
        Add-Success "Application pool exists: $AppPoolName"
        
        # Check app pool settings
        if ($appPool.ManagedRuntimeVersion -eq "v4.0") {
            Add-Success "Application pool using correct .NET version: v4.0"
        } else {
            Add-Warning "Application pool using .NET version: $($appPool.ManagedRuntimeVersion) (expected: v4.0)"
        }
        
        if ($appPool.State -eq "Started") {
            Add-Success "Application pool is running"
        } else {
            Add-Warning "Application pool state: $($appPool.State)"
        }
    } else {
        Add-Error "Application pool not found: $AppPoolName"
    }
    
    # Check if application exists
    $defaultSite = "Default Web Site"
    $app = Get-WebApplication -Site $defaultSite -Name $SiteName -ErrorAction SilentlyContinue
    if ($app) {
        Add-Success "Web application exists: /$SiteName"
        
        if ($app.PhysicalPath -eq $TargetPath) {
            Add-Success "Application physical path is correct"
        } else {
            Add-Warning "Application physical path: $($app.PhysicalPath) (expected: $TargetPath)"
        }
        
        if ($app.ApplicationPool -eq $AppPoolName) {
            Add-Success "Application using correct app pool"
        } else {
            Add-Warning "Application using app pool: $($app.ApplicationPool) (expected: $AppPoolName)"
        }
    } else {
        # Check if it's a separate site
        $site = Get-Website -Name $SiteName -ErrorAction SilentlyContinue
        if ($site) {
            Add-Success "IIS site exists: $SiteName"
        } else {
            Add-Error "Web application or site not found: $SiteName"
        }
    }
} else {
    Add-Warning "WebAdministration module not available - skipping IIS configuration checks"
}

# Check logs directory
Write-Host ""
Write-Host "Checking logs directory..." -ForegroundColor Cyan
$logsPath = "C:\Arctec\Log"
if (Test-Path $logsPath) {
    Add-Success "Logs directory exists: $logsPath"
} else {
    Add-Warning "Logs directory not found: $logsPath"
}

# Test web connectivity
Write-Host ""
Write-Host "Testing web connectivity..." -ForegroundColor Cyan

$urls = @(
    "$BaseUrl/$SiteName",
    "$BaseUrl/$SiteName/PublicService.asmx",
    "$BaseUrl/$SiteName/PublicService.asmx?WSDL"
)

foreach ($url in $urls) {
    try {
        $response = Invoke-WebRequest -Uri $url -UseBasicParsing -TimeoutSec 10 -ErrorAction Stop
        if ($response.StatusCode -eq 200) {
            Add-Success "URL accessible: $url"
        } else {
            Add-Warning "URL returned status $($response.StatusCode): $url"
        }
    } catch {
        Add-Error "URL not accessible: $url - $($_.Exception.Message)"
    }
}

# Check file permissions
Write-Host ""
Write-Host "Checking file permissions..." -ForegroundColor Cyan

try {
    $acl = Get-Acl $TargetPath
    $hasIISUsers = $acl.Access | Where-Object { $_.IdentityReference -like "*IIS_IUSRS*" }
    $hasAppPool = $acl.Access | Where-Object { $_.IdentityReference -like "*IIS AppPool\$AppPoolName*" }
    
    if ($hasIISUsers) {
        Add-Success "IIS_IUSRS has permissions on target directory"
    } else {
        Add-Warning "IIS_IUSRS permissions not found on target directory"
    }
    
    if ($hasAppPool) {
        Add-Success "Application pool identity has permissions on target directory"
    } else {
        Add-Warning "Application pool identity permissions not found on target directory"
    }
} catch {
    Add-Warning "Could not check file permissions: $($_.Exception.Message)"
}

# Summary
Write-Host ""
Write-Host "=== Verification Summary ===" -ForegroundColor Green

if ($errors.Count -eq 0 -and $warnings.Count -eq 0) {
    Write-Host "🎉 Deployment verification completed successfully!" -ForegroundColor Green
    Write-Host "All checks passed. The application should be working correctly." -ForegroundColor Green
} elseif ($errors.Count -eq 0) {
    Write-Host "✅ Deployment verification completed with warnings." -ForegroundColor Yellow
    Write-Host "The application should be working, but there are some issues to review." -ForegroundColor Yellow
} else {
    Write-Host "❌ Deployment verification found errors." -ForegroundColor Red
    Write-Host "The application may not work correctly until these issues are resolved." -ForegroundColor Red
}

Write-Host ""
Write-Host "Errors: $($errors.Count)" -ForegroundColor $(if ($errors.Count -eq 0) { "Green" } else { "Red" })
Write-Host "Warnings: $($warnings.Count)" -ForegroundColor $(if ($warnings.Count -eq 0) { "Green" } else { "Yellow" })

if ($errors.Count -gt 0) {
    Write-Host ""
    Write-Host "Errors found:" -ForegroundColor Red
    foreach ($error in $errors) {
        Write-Host "  - $error" -ForegroundColor Red
    }
}

if ($warnings.Count -gt 0) {
    Write-Host ""
    Write-Host "Warnings found:" -ForegroundColor Yellow
    foreach ($warning in $warnings) {
        Write-Host "  - $warning" -ForegroundColor Yellow
    }
}

Write-Host ""
Write-Host "Next steps:" -ForegroundColor Cyan
Write-Host "1. Visit: $BaseUrl/$SiteName" -ForegroundColor White
Write-Host "2. Test web service: $BaseUrl/$SiteName/PublicService.asmx" -ForegroundColor White
Write-Host "3. Check logs in: $logsPath" -ForegroundColor White

# Return appropriate exit code
if ($errors.Count -gt 0) {
    exit 1
} else {
    exit 0
}
