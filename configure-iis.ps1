# ShinKenShinKunServer IIS Configuration Script
# This script configures IIS for the ShinKenShinKunServer application

param(
    [string]$TargetPath = "C:\inetpub\wwwroot\shin.local",
    [string]$SiteName = "shin.local",
    [string]$AppPoolName = "ShinKenShinKunServerPool",
    [switch]$CreateSite = $false,
    [switch]$Force = $false,
    [int]$Port = 80
)

# Import required modules
try {
    Import-Module WebAdministration -ErrorAction Stop
    Write-Host "WebAdministration module loaded successfully" -ForegroundColor Green
} catch {
    Write-Host "ERROR: WebAdministration module not available. Please ensure IIS Management Tools are installed." -ForegroundColor Red
    Write-Host "To install: Enable-WindowsOptionalFeature -Online -FeatureName IIS-WebServerRole,IIS-WebServer,IIS-CommonHttpFeatures,IIS-HttpErrors,IIS-HttpLogging,IIS-RequestFiltering,IIS-StaticContent,IIS-DefaultDocument,IIS-DirectoryBrowsing,IIS-ASPNET45,IIS-NetFxExtensibility45,IIS-ISAPIExtensions,IIS-ISAPIFilter,IIS-HttpCompressionStatic,IIS-ManagementConsole" -ForegroundColor Yellow
    exit 1
}

Write-Host "=== ShinKenShinKunServer IIS Configuration ===" -ForegroundColor Green
Write-Host "Target Path: $TargetPath" -ForegroundColor Yellow
Write-Host "Site Name: $SiteName" -ForegroundColor Yellow
Write-Host "App Pool: $AppPoolName" -ForegroundColor Yellow
Write-Host "Create Site: $CreateSite" -ForegroundColor Yellow
Write-Host "Force: $Force" -ForegroundColor Yellow

# Function to check if running as administrator
function Test-Administrator {
    $currentUser = [Security.Principal.WindowsIdentity]::GetCurrent()
    $principal = New-Object Security.Principal.WindowsPrincipal($currentUser)
    return $principal.IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)
}

# Function to create application pool
function New-ApplicationPool {
    param([string]$Name)
    
    Write-Host "Configuring application pool: $Name" -ForegroundColor Cyan
    
    # Remove existing app pool if Force is specified
    if (Get-IISAppPool -Name $Name -ErrorAction SilentlyContinue) {
        if ($Force) {
            Write-Host "Removing existing application pool: $Name" -ForegroundColor Yellow
            Remove-WebAppPool -Name $Name -ErrorAction SilentlyContinue
            Start-Sleep -Seconds 2
        } else {
            Write-Host "Application pool already exists: $Name" -ForegroundColor Yellow
            Write-Host "Use -Force to recreate it" -ForegroundColor Yellow
            return $false
        }
    }
    
    try {
        # Create new application pool
        New-WebAppPool -Name $Name -Force
        Write-Host "Application pool created: $Name" -ForegroundColor Green
        
        # Configure application pool settings
        Set-ItemProperty -Path "IIS:\AppPools\$Name" -Name "processModel.identityType" -Value "ApplicationPoolIdentity"
        Set-ItemProperty -Path "IIS:\AppPools\$Name" -Name "managedRuntimeVersion" -Value "v4.0"
        Set-ItemProperty -Path "IIS:\AppPools\$Name" -Name "enable32BitAppOnWin64" -Value $false
        Set-ItemProperty -Path "IIS:\AppPools\$Name" -Name "processModel.idleTimeout" -Value "00:00:00"
        Set-ItemProperty -Path "IIS:\AppPools\$Name" -Name "recycling.periodicRestart.time" -Value "00:00:00"
        
        Write-Host "Application pool configured successfully" -ForegroundColor Green
        
        # Start the application pool
        Start-WebAppPool -Name $Name
        Write-Host "Application pool started: $Name" -ForegroundColor Green
        
        return $true
    } catch {
        Write-Host "ERROR: Failed to create application pool: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# Function to create IIS site
function New-IISSite {
    param(
        [string]$Name,
        [string]$Path,
        [string]$AppPool,
        [int]$Port = 80
    )
    
    Write-Host "Configuring IIS site: $Name" -ForegroundColor Cyan
    
    # Remove existing site if Force is specified
    if (Get-Website -Name $Name -ErrorAction SilentlyContinue) {
        if ($Force) {
            Write-Host "Removing existing site: $Name" -ForegroundColor Yellow
            Remove-Website -Name $Name -ErrorAction SilentlyContinue
            Start-Sleep -Seconds 2
        } else {
            Write-Host "Site already exists: $Name" -ForegroundColor Yellow
            Write-Host "Use -Force to recreate it" -ForegroundColor Yellow
            return $false
        }
    }
    
    try {
        New-Website -Name $Name -PhysicalPath $Path -ApplicationPool $AppPool -Port $Port
        Write-Host "IIS site created successfully: $Name" -ForegroundColor Green
        return $true
    } catch {
        Write-Host "ERROR: Failed to create IIS site: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# Function to create web application
function New-WebApp {
    param(
        [string]$SiteName,
        [string]$AppName,
        [string]$Path,
        [string]$AppPool
    )
    
    Write-Host "Configuring web application: /$AppName" -ForegroundColor Cyan
    
    # Remove existing application if Force is specified
    if (Get-WebApplication -Site $SiteName -Name $AppName -ErrorAction SilentlyContinue) {
        if ($Force) {
            Write-Host "Removing existing application: /$AppName" -ForegroundColor Yellow
            Remove-WebApplication -Site $SiteName -Name $AppName -ErrorAction SilentlyContinue
            Start-Sleep -Seconds 2
        } else {
            Write-Host "Application already exists: /$AppName" -ForegroundColor Yellow
            Write-Host "Use -Force to recreate it" -ForegroundColor Yellow
            return $false
        }
    }
    
    try {
        New-WebApplication -Site $SiteName -Name $AppName -PhysicalPath $Path -ApplicationPool $AppPool
        Write-Host "Web application created successfully: /$AppName" -ForegroundColor Green
        return $true
    } catch {
        Write-Host "ERROR: Failed to create web application: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# Function to set folder permissions
function Set-FolderPermissions {
    param([string]$Path, [string]$AppPoolName)
    
    Write-Host "Setting folder permissions for: $Path" -ForegroundColor Cyan
    
    try {
        # Get current ACL
        $acl = Get-Acl $Path
        
        # Grant IIS_IUSRS read and execute permissions
        $accessRule1 = New-Object System.Security.AccessControl.FileSystemAccessRule(
            "IIS_IUSRS", 
            "ReadAndExecute", 
            "ContainerInherit,ObjectInherit", 
            "None", 
            "Allow"
        )
        $acl.SetAccessRule($accessRule1)
        
        # Grant application pool identity modify permissions
        $accessRule2 = New-Object System.Security.AccessControl.FileSystemAccessRule(
            "IIS AppPool\$AppPoolName", 
            "Modify", 
            "ContainerInherit,ObjectInherit", 
            "None", 
            "Allow"
        )
        $acl.SetAccessRule($accessRule2)
        
        # Apply the ACL
        Set-Acl -Path $Path -AclObject $acl
        
        Write-Host "Folder permissions set successfully" -ForegroundColor Green
        return $true
    } catch {
        Write-Host "ERROR: Failed to set folder permissions: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# Main configuration logic
try {
    # Check if running as administrator
    if (-not (Test-Administrator)) {
        throw "This script must be run as Administrator to configure IIS."
    }
    
    # Verify target path exists
    if (-not (Test-Path $TargetPath)) {
        throw "Target path does not exist: $TargetPath. Please run the deployment script first."
    }
    
    # Create application pool
    $appPoolSuccess = New-ApplicationPool -Name $AppPoolName
    if (-not $appPoolSuccess) {
        throw "Failed to create or configure application pool"
    }
    
    # Create IIS site or web application
    if ($CreateSite) {
        $siteSuccess = New-IISSite -Name $SiteName -Path $TargetPath -AppPool $AppPoolName -Port $Port
        if (-not $siteSuccess) {
            throw "Failed to create IIS site"
        }
    } else {
        # Create application under Default Web Site
        $defaultSite = "Default Web Site"
        if (-not (Get-Website -Name $defaultSite -ErrorAction SilentlyContinue)) {
            throw "Default Web Site not found. Use -CreateSite to create a separate site."
        }
        
        $appSuccess = New-WebApp -SiteName $defaultSite -AppName $SiteName -Path $TargetPath -AppPool $AppPoolName
        if (-not $appSuccess) {
            throw "Failed to create web application"
        }
    }
    
    # Set folder permissions
    $permSuccess = Set-FolderPermissions -Path $TargetPath -AppPoolName $AppPoolName
    if (-not $permSuccess) {
        Write-Host "WARNING: Failed to set some folder permissions" -ForegroundColor Yellow
    }
    
    # Configure logs directory permissions
    $logsPath = "C:\Arctec\Log"
    if (Test-Path $logsPath) {
        Write-Host "Setting permissions for logs directory: $logsPath" -ForegroundColor Cyan
        $permLogsSuccess = Set-FolderPermissions -Path $logsPath -AppPoolName $AppPoolName
        if (-not $permLogsSuccess) {
            Write-Host "WARNING: Failed to set logs directory permissions" -ForegroundColor Yellow
        }
    }
    
    # Display configuration summary
    Write-Host ""
    Write-Host "=== Configuration Summary ===" -ForegroundColor Green
    Write-Host "Application Pool: $AppPoolName" -ForegroundColor White
    Write-Host "Target Path: $TargetPath" -ForegroundColor White
    if ($CreateSite) {
        Write-Host "IIS Site: $SiteName" -ForegroundColor White
        Write-Host "URL: http://localhost:$Port" -ForegroundColor White
    } else {
        Write-Host "Web Application: /$SiteName" -ForegroundColor White
        Write-Host "URL: http://localhost/$SiteName" -ForegroundColor White
    }
    Write-Host "Service URL: http://localhost$(if ($CreateSite) {":$Port"} else {"/$SiteName"})/PublicService.asmx" -ForegroundColor White
    Write-Host ""
    Write-Host "IIS configuration completed successfully!" -ForegroundColor Green
    
} catch {
    Write-Host ""
    Write-Host "=== Configuration Failed ===" -ForegroundColor Red
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host ""
    Write-Host "Troubleshooting steps:" -ForegroundColor Yellow
    Write-Host "1. Ensure you're running as Administrator" -ForegroundColor White
    Write-Host "2. Verify IIS and ASP.NET features are installed" -ForegroundColor White
    Write-Host "3. Check that the target directory exists" -ForegroundColor White
    Write-Host "4. Run: Get-WindowsFeature *IIS* to check installed features" -ForegroundColor White
    exit 1
}
