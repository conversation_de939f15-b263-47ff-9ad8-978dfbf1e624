@echo off
REM ShinKenShinKunServer IIS Deployment Batch Script
REM This script builds and deploys the ShinKenShinKunServer to IIS
REM Target Path: C:\inetpub\wwwroot\shin.local

setlocal enabledelayedexpansion

REM Configuration variables
set TARGET_PATH=C:\inetpub\wwwroot\shin.local
set SITE_NAME=shin.local
set APP_POOL_NAME=ShinKenShinKunServerPool
set BUILD_CONFIG=Release

REM Script paths
set SCRIPT_DIR=%~dp0
set PROJECT_PATH=%SCRIPT_DIR%src\Presentation\WebApi\ShinKenShinKunServer
set PROJECT_FILE=%PROJECT_PATH%\ShinKenShinKunServer.csproj
set SOLUTION_FILE=%SCRIPT_DIR%ShinKenShinKun.sln

echo ===============================================
echo ShinKenShinKunServer IIS Deployment Script
echo ===============================================
echo Target Path: %TARGET_PATH%
echo Site Name: %SITE_NAME%
echo App Pool: %APP_POOL_NAME%
echo Build Configuration: %BUILD_CONFIG%
echo ===============================================

REM Check if running as administrator
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo ERROR: This script must be run as Administrator to deploy to IIS.
    echo Please right-click and select "Run as administrator"
    pause
    exit /b 1
)

REM Verify project files exist
if not exist "%PROJECT_FILE%" (
    echo ERROR: Project file not found: %PROJECT_FILE%
    pause
    exit /b 1
)

if not exist "%SOLUTION_FILE%" (
    echo ERROR: Solution file not found: %SOLUTION_FILE%
    pause
    exit /b 1
)

REM Find MSBuild
set MSBUILD_PATH=
for %%p in (
    "%ProgramFiles%\Microsoft Visual Studio\2022\Enterprise\MSBuild\Current\Bin\MSBuild.exe"
    "%ProgramFiles%\Microsoft Visual Studio\2022\Professional\MSBuild\Current\Bin\MSBuild.exe"
    "%ProgramFiles%\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MSBuild.exe"
    "%ProgramFiles(x86)%\Microsoft Visual Studio\2019\Enterprise\MSBuild\Current\Bin\MSBuild.exe"
    "%ProgramFiles(x86)%\Microsoft Visual Studio\2019\Professional\MSBuild\Current\Bin\MSBuild.exe"
    "%ProgramFiles(x86)%\Microsoft Visual Studio\2019\Community\MSBuild\Current\Bin\MSBuild.exe"
    "%ProgramFiles(x86)%\Microsoft Visual Studio\2017\Enterprise\MSBuild\15.0\Bin\MSBuild.exe"
    "%ProgramFiles(x86)%\Microsoft Visual Studio\2017\Professional\MSBuild\15.0\Bin\MSBuild.exe"
    "%ProgramFiles(x86)%\Microsoft Visual Studio\2017\Community\MSBuild\15.0\Bin\MSBuild.exe"
    "%ProgramFiles(x86)%\MSBuild\14.0\Bin\MSBuild.exe"
) do (
    if exist "%%p" (
        set MSBUILD_PATH=%%p
        goto :found_msbuild
    )
)

REM Try to find MSBuild in PATH
where msbuild.exe >nul 2>&1
if %errorLevel% equ 0 (
    for /f "tokens=*" %%i in ('where msbuild.exe') do (
        set MSBUILD_PATH=%%i
        goto :found_msbuild
    )
)

echo ERROR: MSBuild not found. Please install Visual Studio or Build Tools for Visual Studio.
pause
exit /b 1

:found_msbuild
echo Using MSBuild: %MSBUILD_PATH%

REM Build the project
echo.
echo Building ShinKenShinKunServer...
"%MSBUILD_PATH%" "%PROJECT_FILE%" /p:Configuration=%BUILD_CONFIG% /p:Platform="Any CPU" /p:OutputPath=bin\ /t:Build /verbosity:minimal

if %errorLevel% neq 0 (
    echo ERROR: Build failed with exit code: %errorLevel%
    pause
    exit /b 1
)

echo Build completed successfully

REM Create target directory if it doesn't exist
if not exist "%TARGET_PATH%" (
    echo Creating target directory: %TARGET_PATH%
    mkdir "%TARGET_PATH%"
)

REM Copy application files
echo.
echo Copying application files to: %TARGET_PATH%

REM Copy specific files
if exist "%PROJECT_PATH%\Web.config" (
    echo   Copying: Web.config
    copy "%PROJECT_PATH%\Web.config" "%TARGET_PATH%\" >nul
)

if exist "%PROJECT_PATH%\index.html" (
    echo   Copying: index.html
    copy "%PROJECT_PATH%\index.html" "%TARGET_PATH%\" >nul
)

if exist "%PROJECT_PATH%\PublicService.asmx" (
    echo   Copying: PublicService.asmx
    copy "%PROJECT_PATH%\PublicService.asmx" "%TARGET_PATH%\" >nul
)

REM Copy bin directory
if exist "%PROJECT_PATH%\bin" (
    echo   Copying: bin directory
    if exist "%TARGET_PATH%\bin" rmdir /s /q "%TARGET_PATH%\bin"
    xcopy "%PROJECT_PATH%\bin" "%TARGET_PATH%\bin\" /E /I /Y >nul
)

REM Copy other common web files
for %%f in (*.aspx *.htm *.css *.js) do (
    if exist "%PROJECT_PATH%\%%f" (
        echo   Copying: %%f
        copy "%PROJECT_PATH%\%%f" "%TARGET_PATH%\" >nul
    )
)

REM Copy directories if they exist
for %%d in (App_Data Images Scripts Content) do (
    if exist "%PROJECT_PATH%\%%d" (
        echo   Copying: %%d directory
        xcopy "%PROJECT_PATH%\%%d" "%TARGET_PATH%\%%d\" /E /I /Y >nul
    )
)

echo Application files copied successfully

REM Create logs directory if specified in config
set LOGS_PATH=C:\Arctec\Log
if not exist "%LOGS_PATH%" (
    echo Creating logs directory: %LOGS_PATH%
    mkdir "%LOGS_PATH%"
)

echo.
echo ===============================================
echo Deployment Summary
echo ===============================================
echo Project: ShinKenShinKunServer
echo Target Path: %TARGET_PATH%
echo Application Pool: %APP_POOL_NAME%
echo Application: /%SITE_NAME% (under Default Web Site)
echo URL: http://localhost/%SITE_NAME%
echo Service URL: http://localhost/%SITE_NAME%/PublicService.asmx
echo ===============================================
echo.
echo Deployment completed successfully!
echo.
echo NEXT STEPS:
echo 1. Configure IIS Application Pool and Application manually in IIS Manager
echo 2. Set the Application Pool to use .NET Framework v4.0
echo 3. Ensure the Application Pool identity has proper permissions
echo 4. Test the application by visiting the URLs above
echo.

pause
