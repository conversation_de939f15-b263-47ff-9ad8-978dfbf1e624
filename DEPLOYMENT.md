# ShinKenShinKunServer IIS Deployment Guide

This guide provides instructions for deploying the ShinKenShinKunServer web application to IIS.

## Prerequisites

### System Requirements
- Windows Server 2016+ or Windows 10+ with IIS enabled
- .NET Framework 4.8 or later
- IIS with ASP.NET support enabled
- Administrator privileges

### IIS Features Required
Ensure the following IIS features are enabled:
- IIS Management Console
- World Wide Web Services
- Application Development Features:
  - .NET Framework 4.8
  - ASP.NET 4.8
  - ISAPI Extensions
  - ISAPI Filters

### Development Tools (for building)
- Visual Studio 2017+ or MSBuild Tools
- .NET Framework 4.8 SDK

## Deployment Options

### Option 1: Automated PowerShell Deployment (Recommended)

The PowerShell script provides the most comprehensive deployment with automatic IIS configuration.

#### Usage
```powershell
# Basic deployment (creates application under Default Web Site)
.\deploy-to-iis.ps1

# Create a separate IIS site
.\deploy-to-iis.ps1 -CreateSite

# Force overwrite existing deployment
.\deploy-to-iis.ps1 -Force

# Custom parameters
.\deploy-to-iis.ps1 -TargetPath "C:\inetpub\wwwroot\myapp" -SiteName "myapp" -AppPoolName "MyAppPool"
```

#### Parameters
- `TargetPath`: Target deployment directory (default: `C:\inetpub\wwwroot\shin.local`)
- `SiteName`: IIS site or application name (default: `shin.local`)
- `AppPoolName`: Application pool name (default: `ShinKenShinKunServerPool`)
- `BuildConfiguration`: Build configuration (default: `Release`)
- `CreateSite`: Create a separate IIS site instead of an application
- `Force`: Overwrite existing deployment

### Option 2: Batch Script Deployment

The batch script provides basic file deployment without IIS configuration.

#### Usage
```cmd
REM Run as Administrator
deploy-to-iis.bat
```

### Option 3: Manual Deployment

#### Step 1: Build the Project
```cmd
# Using MSBuild
msbuild "src\Presentation\WebApi\ShinKenShinKunServer\ShinKenShinKunServer.csproj" /p:Configuration=Release /p:Platform="Any CPU"

# Or using Visual Studio
# Open ShinKenShinKun.sln and build the ShinKenShinKunServer project in Release mode
```

#### Step 2: Create Target Directory
```cmd
mkdir C:\inetpub\wwwroot\shin.local
```

#### Step 3: Copy Files
Copy the following files and folders from the project directory to `C:\inetpub\wwwroot\shin.local`:
- `Web.config`
- `index.html`
- `PublicService.asmx`
- `bin\` directory (entire folder)
- Any additional web files (*.aspx, *.htm, *.css, *.js)

#### Step 4: Configure IIS

1. **Create Application Pool:**
   - Open IIS Manager
   - Right-click "Application Pools" → "Add Application Pool"
   - Name: `ShinKenShinKunServerPool`
   - .NET CLR Version: `.NET CLR Version v4.0.30319`
   - Managed Pipeline Mode: `Integrated`
   - Click "OK"

2. **Create Application:**
   - Expand "Sites" → "Default Web Site"
   - Right-click "Default Web Site" → "Add Application"
   - Alias: `shin.local`
   - Application Pool: `ShinKenShinKunServerPool`
   - Physical Path: `C:\inetpub\wwwroot\shin.local`
   - Click "OK"

#### Step 5: Set Permissions
```cmd
# Grant IIS_IUSRS read access
icacls "C:\inetpub\wwwroot\shin.local" /grant "IIS_IUSRS:(OI)(CI)RX"

# Grant application pool identity modify access to logs directory
mkdir "C:\Arctec\Log"
icacls "C:\Arctec\Log" /grant "IIS AppPool\ShinKenShinKunServerPool:(OI)(CI)F"
```

## Configuration

### Web.config Settings
The application uses the following key configuration settings:

```xml
<appSettings>
    <add key="CheckTime" value="500"/>
    <add key="CheckCount" value="3"/>
    <add key="LoggerPass" value="C:\Arctec\Log"/>
    <add key="LoggerName" value="ServiceLog"/>
    <add key="LimitDay" value="90"/>
</appSettings>

<connectionStrings>
    <add name="MedicalCheckupConnectionString" 
         connectionString="Data Source=localhost,1435;Initial Catalog=GenkiPlazaDatabase2;Persist Security Info=True;User ID=sa;Password=StrongP@ssw0rd123;TrustServerCertificate=True" 
         providerName="System.Data.SqlClient"/>
</connectionStrings>
```

### Database Configuration
Update the connection strings in `Web.config` to match your database server settings.

## Testing the Deployment

### URLs to Test
- Application Home: `http://localhost/shin.local`
- Web Service: `http://localhost/shin.local/PublicService.asmx`
- Service WSDL: `http://localhost/shin.local/PublicService.asmx?WSDL`

### Verification Steps
1. Browse to the application home page
2. Verify the web service is accessible
3. Check that the WSDL loads correctly
4. Monitor IIS logs for any errors
5. Check application logs in `C:\Arctec\Log`

## Troubleshooting

### Common Issues

#### Build Errors
- Ensure .NET Framework 4.8 SDK is installed
- Verify all NuGet packages are restored
- Check that MSBuild can find all references

#### IIS Configuration Errors
- Verify ASP.NET 4.8 is installed and enabled in IIS
- Check that the application pool is using the correct .NET version
- Ensure the application pool identity has proper permissions

#### Runtime Errors
- Check IIS logs in `C:\inetpub\logs\LogFiles`
- Review application logs in `C:\Arctec\Log`
- Verify database connectivity and permissions
- Check that all required assemblies are in the `bin` directory

#### Permission Issues
```cmd
# Reset permissions on the application directory
icacls "C:\inetpub\wwwroot\shin.local" /reset /T
icacls "C:\inetpub\wwwroot\shin.local" /grant "IIS_IUSRS:(OI)(CI)RX"
icacls "C:\inetpub\wwwroot\shin.local" /grant "IIS AppPool\ShinKenShinKunServerPool:(OI)(CI)RX"
```

### Log Locations
- IIS Logs: `C:\inetpub\logs\LogFiles\W3SVC1\`
- Application Logs: `C:\Arctec\Log\`
- Windows Event Logs: Event Viewer → Windows Logs → Application

## Security Considerations

1. **Database Security:** Use strong passwords and consider using Windows Authentication
2. **File Permissions:** Grant minimal required permissions to IIS accounts
3. **Network Security:** Consider using HTTPS in production environments
4. **Application Pool Identity:** Use a dedicated service account if required

## Maintenance

### Regular Tasks
- Monitor log files for errors
- Clean up old log files (application setting: `LimitDay`)
- Update connection strings as needed
- Apply security updates to .NET Framework and IIS

### Backup Recommendations
- Backup the application directory: `C:\inetpub\wwwroot\shin.local`
- Backup IIS configuration
- Backup application logs: `C:\Arctec\Log`
- Backup database regularly
