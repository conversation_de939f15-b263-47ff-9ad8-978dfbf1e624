# Quick Fix Script for ShinKenShinKunServer Deployment Issues
# This script addresses the specific errors found in verification

param(
    [string]$TargetPath = "C:\inetpub\wwwroot\shin.local",
    [string]$SiteName = "shin.local",
    [string]$AppPoolName = "ShinKenShinKunServerPool"
)

Write-Host "=== ShinKenShinKunServer Deployment Fix ===" -ForegroundColor Green
Write-Host "Fixing deployment issues..." -ForegroundColor Yellow

# Function to check if running as administrator
function Test-Administrator {
    $currentUser = [Security.Principal.WindowsIdentity]::GetCurrent()
    $principal = New-Object Security.Principal.WindowsPrincipal($currentUser)
    return $principal.IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)
}

try {
    # Check if running as administrator
    if (-not (Test-Administrator)) {
        throw "This script must be run as Administrator."
    }

    # Import WebAdministration module
    try {
        Import-Module WebAdministration -ErrorAction Stop
        Write-Host "✅ WebAdministration module loaded" -ForegroundColor Green
    } catch {
        Write-Host "❌ WebAdministration module not available" -ForegroundColor Red
        Write-Host "Installing IIS Management Tools..." -ForegroundColor Yellow
        
        # Try to enable IIS features
        try {
            Enable-WindowsOptionalFeature -Online -FeatureName IIS-WebServerRole,IIS-WebServer,IIS-CommonHttpFeatures,IIS-HttpErrors,IIS-HttpLogging,IIS-RequestFiltering,IIS-StaticContent,IIS-DefaultDocument,IIS-DirectoryBrowsing,IIS-ASPNET45,IIS-NetFxExtensibility45,IIS-ISAPIExtensions,IIS-ISAPIFilter,IIS-ManagementConsole -All -NoRestart
            Write-Host "✅ IIS features enabled. Please restart and run this script again." -ForegroundColor Green
            exit 0
        } catch {
            Write-Host "❌ Failed to enable IIS features. Please enable IIS manually through Windows Features." -ForegroundColor Red
            exit 1
        }
    }

    # Fix 1: Create Application Pool
    Write-Host ""
    Write-Host "🔧 Creating Application Pool: $AppPoolName" -ForegroundColor Cyan
    
    # Remove existing app pool if it exists but is misconfigured
    if (Get-IISAppPool -Name $AppPoolName -ErrorAction SilentlyContinue) {
        Write-Host "Removing existing application pool..." -ForegroundColor Yellow
        Remove-WebAppPool -Name $AppPoolName -ErrorAction SilentlyContinue
        Start-Sleep -Seconds 3
    }
    
    # Create new application pool
    New-WebAppPool -Name $AppPoolName
    Write-Host "✅ Application pool created: $AppPoolName" -ForegroundColor Green
    
    # Configure application pool settings
    Set-ItemProperty -Path "IIS:\AppPools\$AppPoolName" -Name "processModel.identityType" -Value "ApplicationPoolIdentity"
    Set-ItemProperty -Path "IIS:\AppPools\$AppPoolName" -Name "managedRuntimeVersion" -Value "v4.0"
    Set-ItemProperty -Path "IIS:\AppPools\$AppPoolName" -Name "enable32BitAppOnWin64" -Value $false
    Set-ItemProperty -Path "IIS:\AppPools\$AppPoolName" -Name "processModel.idleTimeout" -Value "00:00:00"
    
    Write-Host "✅ Application pool configured for .NET Framework 4.0" -ForegroundColor Green
    
    # Start the application pool
    Start-WebAppPool -Name $AppPoolName
    Write-Host "✅ Application pool started" -ForegroundColor Green

    # Fix 2: Create Web Application (if it doesn't exist)
    Write-Host ""
    Write-Host "🔧 Configuring Web Application: /$SiteName" -ForegroundColor Cyan
    
    $defaultSite = "Default Web Site"
    
    # Check if Default Web Site exists
    if (-not (Get-Website -Name $defaultSite -ErrorAction SilentlyContinue)) {
        Write-Host "❌ Default Web Site not found. Creating it..." -ForegroundColor Yellow
        New-Website -Name $defaultSite -PhysicalPath "C:\inetpub\wwwroot" -Port 80
    }
    
    # Remove existing application if it exists
    if (Get-WebApplication -Site $defaultSite -Name $SiteName -ErrorAction SilentlyContinue) {
        Write-Host "Removing existing web application..." -ForegroundColor Yellow
        Remove-WebApplication -Site $defaultSite -Name $SiteName
        Start-Sleep -Seconds 2
    }
    
    # Create web application
    New-WebApplication -Site $defaultSite -Name $SiteName -PhysicalPath $TargetPath -ApplicationPool $AppPoolName
    Write-Host "✅ Web application created: /$SiteName" -ForegroundColor Green

    # Fix 3: Set Proper Permissions
    Write-Host ""
    Write-Host "🔧 Setting Folder Permissions" -ForegroundColor Cyan
    
    if (Test-Path $TargetPath) {
        # Get current ACL
        $acl = Get-Acl $TargetPath
        
        # Grant IIS_IUSRS read and execute permissions
        $accessRule1 = New-Object System.Security.AccessControl.FileSystemAccessRule(
            "IIS_IUSRS", 
            "ReadAndExecute", 
            "ContainerInherit,ObjectInherit", 
            "None", 
            "Allow"
        )
        $acl.SetAccessRule($accessRule1)
        
        # Grant application pool identity modify permissions
        $accessRule2 = New-Object System.Security.AccessControl.FileSystemAccessRule(
            "IIS AppPool\$AppPoolName", 
            "Modify", 
            "ContainerInherit,ObjectInherit", 
            "None", 
            "Allow"
        )
        $acl.SetAccessRule($accessRule2)
        
        # Apply the ACL
        Set-Acl -Path $TargetPath -AclObject $acl
        
        Write-Host "✅ Permissions set for: $TargetPath" -ForegroundColor Green
    } else {
        Write-Host "❌ Target path not found: $TargetPath" -ForegroundColor Red
        Write-Host "Please run the deployment script first to copy files." -ForegroundColor Yellow
    }

    # Fix 4: Configure Logs Directory
    Write-Host ""
    Write-Host "🔧 Configuring Logs Directory" -ForegroundColor Cyan
    
    $logsPath = "C:\Arctec\Log"
    if (-not (Test-Path $logsPath)) {
        New-Item -ItemType Directory -Path $logsPath -Force | Out-Null
        Write-Host "✅ Created logs directory: $logsPath" -ForegroundColor Green
    }
    
    # Set permissions for logs directory
    $logsAcl = Get-Acl $logsPath
    $logsAccessRule = New-Object System.Security.AccessControl.FileSystemAccessRule(
        "IIS AppPool\$AppPoolName", 
        "FullControl", 
        "ContainerInherit,ObjectInherit", 
        "None", 
        "Allow"
    )
    $logsAcl.SetAccessRule($logsAccessRule)
    Set-Acl -Path $logsPath -AclObject $logsAcl
    
    Write-Host "✅ Permissions set for logs directory" -ForegroundColor Green

    # Restart IIS to ensure all changes take effect
    Write-Host ""
    Write-Host "🔧 Restarting IIS..." -ForegroundColor Cyan
    iisreset /noforce
    Write-Host "✅ IIS restarted" -ForegroundColor Green

    # Display success summary
    Write-Host ""
    Write-Host "=== Fix Completed Successfully! ===" -ForegroundColor Green
    Write-Host "✅ Application Pool: $AppPoolName" -ForegroundColor White
    Write-Host "✅ Web Application: /$SiteName" -ForegroundColor White
    Write-Host "✅ Folder Permissions: Set" -ForegroundColor White
    Write-Host "✅ Logs Directory: Configured" -ForegroundColor White
    Write-Host ""
    Write-Host "🌐 Test URLs:" -ForegroundColor Cyan
    Write-Host "   Home: http://localhost/$SiteName" -ForegroundColor White
    Write-Host "   Service: http://localhost/$SiteName/PublicService.asmx" -ForegroundColor White
    Write-Host ""
    Write-Host "Run verify-deployment.ps1 to confirm everything is working!" -ForegroundColor Yellow

} catch {
    Write-Host ""
    Write-Host "=== Fix Failed ===" -ForegroundColor Red
    Write-Host "❌ Error: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host ""
    Write-Host "Manual steps to try:" -ForegroundColor Yellow
    Write-Host "1. Open IIS Manager as Administrator" -ForegroundColor White
    Write-Host "2. Create Application Pool '$AppPoolName' with .NET v4.0" -ForegroundColor White
    Write-Host "3. Create Application '/$SiteName' under Default Web Site" -ForegroundColor White
    Write-Host "4. Set Application Pool to '$AppPoolName'" -ForegroundColor White
    Write-Host "5. Set Physical Path to '$TargetPath'" -ForegroundColor White
    exit 1
}
